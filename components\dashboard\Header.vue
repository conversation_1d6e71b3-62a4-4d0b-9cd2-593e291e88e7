<template>
  <header
    class="bg-gradient-to-r from-neutral-50 to-neutral-100 sticky top-0 z-30"
  >
    <div class="p-3 sm:p-4 lg:p-6">
      <div class="flex items-center justify-between gap-2 sm:gap-4">
        <div class="flex items-center gap-2 sm:gap-3 md:gap-4">
          <div
            class="lg:hidden bg-white/90 backdrop-blur-md rounded-2xl shadow-lg border border-white/20"
          >
            <button
              @click="$emit('toggle-mobile-sidebar')"
              class="p-2 sm:p-3 rounded-2xl text-neutral-600 hover:bg-white/50 hover:shadow-md hover:scale-105 transition-all duration-200"
            >
              <Menu class="w-4 h-4 sm:w-5 sm:h-5" />
            </button>
          </div>

          <!-- Logo Section -->
          <!-- Mobile Logo -->
          <NuxtLink to="/dashboard" class="flex items-center lg:hidden">
            <img
              src="/favicon.png"
              alt="Bookiime"
              class="h-12 md:h-14 w-auto object-cover rounded-lg hover:scale-110 transition-transform duration-200"
              style="
                object-position: center;
                image-rendering: -webkit-optimize-contrast;
                image-rendering: crisp-edges;
              "
            />
          </NuxtLink>

          <!-- Desktop Sidebar Toggle Card -->
          <div
            class="hidden lg:block bg-white/90 backdrop-blur-md rounded-2xl shadow-lg border border-white/20"
          >
            <button
              @click="$emit('toggle-sidebar')"
              class="p-2 sm:p-3 rounded-2xl text-neutral-600 hover:bg-white/50 hover:shadow-md hover:scale-105 transition-all duration-200"
            >
              <PanelLeftOpen
                v-if="sidebarCollapsed"
                class="w-4 h-4 sm:w-5 sm:h-5"
              />
              <PanelLeftClose v-else class="w-4 h-4 sm:w-5 sm:h-5" />
            </button>
          </div>

          <!-- Page Title Card -->
          <div
            class="bg-white/90 backdrop-blur-md rounded-2xl shadow-lg border border-white/20 px-3 sm:px-4 md:px-6 py-2 sm:py-3 md:py-4 hidden sm:block"
          >
            <h1 class="text-base sm:text-lg font-semibold text-neutral-900">
              {{ pageTitle }}
            </h1>
            <p v-if="pageSubtitle" class="text-xs sm:text-sm text-neutral-600">
              {{ pageSubtitle }}
            </p>
          </div>
        </div>

        <div class="flex items-center gap-2 sm:gap-3 md:gap-4">
          <!-- Search Card -->
          <div
            class="hidden md:block bg-white/90 backdrop-blur-md rounded-2xl shadow-lg border border-white/20"
          >
            <div class="relative p-2 sm:p-3">
              <Search
                class="absolute left-4 sm:left-6 top-1/2 transform -translate-y-1/2 w-4 h-4 text-neutral-400"
              />
              <input
                type="text"
                placeholder="Search..."
                class="pl-8 sm:pl-10 pr-3 sm:pr-4 py-2 border-0 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 bg-white/50 w-48 md:w-56 lg:w-64 xl:w-72"
              />
            </div>
          </div>

          <!-- Notifications Card -->
          <div
            class="bg-white/90 backdrop-blur-md rounded-2xl shadow-lg border border-white/20"
          >
            <button
              class="relative p-2 sm:p-3 rounded-2xl text-neutral-600 hover:bg-white/50 hover:shadow-md hover:scale-105 transition-all duration-200"
            >
              <Bell class="w-4 h-4 sm:w-5 sm:h-5" />
              <span
                class="absolute top-1 right-1 sm:top-2 sm:right-2 w-2 h-2 bg-red-500 rounded-full shadow-sm animate-pulse"
              ></span>
            </button>
          </div>

          <!-- User Menu Card (Mobile) -->
          <div
            class="lg:hidden bg-white/90 backdrop-blur-md rounded-2xl shadow-lg border border-white/20"
          >
            <div class="relative">
              <button
                @click="showMobileUserMenu = !showMobileUserMenu"
                class="flex items-center space-x-2 p-3 rounded-2xl hover:bg-white/50 hover:shadow-md hover:scale-105 transition-all duration-200"
              >
                <div
                  class="w-8 h-8 bg-gradient-to-br from-primary-100 to-primary-200 rounded-full flex items-center justify-center shadow-sm"
                >
                  <img
                    v-if="currentUser?.avatar"
                    :src="currentUser.avatar"
                    :alt="displayName"
                    class="w-8 h-8 rounded-full object-cover"
                  />
                  <div
                    v-else-if="currentUser?.name"
                    class="w-8 h-8 rounded-full bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center text-white text-xs font-bold"
                  >
                    {{ currentUser.name }}
                  </div>
                  <Icon
                    v-else
                    name="lucide:user"
                    class="w-4 h-4 text-primary-600"
                  />
                </div>
              </button>

              <!-- Mobile User Dropdown -->
              <Transition
                enter-active-class="transition ease-out duration-200"
                enter-from-class="opacity-0 translate-y-1"
                enter-to-class="opacity-100 translate-y-0"
                leave-active-class="transition ease-in duration-150"
                leave-from-class="opacity-100 translate-y-0"
                leave-to-class="opacity-0 translate-y-1"
              >
                <div
                  v-if="showMobileUserMenu"
                  class="absolute right-0 mt-3 w-52 bg-white/95 backdrop-blur-md rounded-xl shadow-xl border border-white/20 py-2 z-50"
                >
                  <div class="px-4 py-3 border-b border-neutral-200">
                    <p class="text-sm font-semibold text-neutral-900">
                      {{ displayName }}
                    </p>
                    <p class="text-xs text-neutral-600">{{ currentUser?.email }}</p>
                  </div>
                  <NuxtLink
                    to="/dashboard/profile"
                    class="flex items-center px-4 py-3 text-sm font-medium text-neutral-700 hover:bg-white/50 transition-all duration-200 rounded-lg mx-2"
                    @click="showMobileUserMenu = false"
                  >
                    <UserIcon class="w-4 h-4 mr-3" />
                    Profile
                  </NuxtLink>
                  <NuxtLink
                    to="/dashboard/settings"
                    class="flex items-center px-4 py-3 text-sm font-medium text-neutral-700 hover:bg-white/50 transition-all duration-200 rounded-lg mx-2"
                    @click="showMobileUserMenu = false"
                  >
                    <Settings class="w-4 h-4 mr-3" />
                    Settings
                  </NuxtLink>
                  <hr class="my-2 border-neutral-200 mx-2" />
                  <button
                    @click="handleLogout"
                    class="flex items-center w-full px-4 py-3 text-sm font-medium text-red-600 hover:bg-red-50/80 transition-all duration-200 rounded-lg mx-2"
                  >
                    <LogOut class="w-4 h-4 mr-3" />
                    Sign Out
                  </button>
                </div>
              </Transition>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import {
  Menu,
  LogOut,
  PanelLeftOpen,
  PanelLeftClose,
  Search,
  Bell,
  User as UserIcon,
  Settings,
} from "lucide-vue-next";

interface User {
  id?: string;
  name?: string;
  email?: string;
  avatar?: string;
}

interface Props {
  user: User | null;
  sidebarCollapsed: boolean;
}

interface Emits {
  (e: "toggle-sidebar"): void;
  (e: "toggle-mobile-sidebar"): void;
  (e: "logout"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Composables
const route = useRoute();

// Get reactive user data from useUser composable for real-time updates
const { user: reactiveUser } = useUser()

// Reactive state
const showMobileUserMenu = ref(false);

// Use reactive user data that updates in real-time, fallback to props
const currentUser = computed(() => reactiveUser.value || props.user)

// Computed properties
const displayName = computed(() => {
  if (currentUser.value?.name) {
    return currentUser.value.name;
  }

  if (currentUser.value?.email) {
    const emailName = currentUser.value.email.split("@")[0];
    return emailName.charAt(0).toUpperCase() + emailName.slice(1);
  }

  return "User";
});


const pageTitle = computed(() => {
  const routeName = route.name as string;
  const titleMap: Record<string, string> = {
    dashboard: "Dashboard",
    "dashboard-services": "Services",
    "dashboard-bookings": "Bookings",
    "dashboard-customers": "Customers",
    "dashboard-calendar": "Calendar",
    "dashboard-analytics": "Analytics",
    "dashboard-profile": "Profile",
    "dashboard-settings": "Settings",
  };

  return titleMap[routeName] || "Dashboard";
});

const pageSubtitle = computed(() => {
  const routeName = route.name as string;
  const subtitleMap: Record<string, string> = {
    dashboard: `${displayName.value}`,
    "dashboard-services": "Manage your services and pricing",
    "dashboard-bookings": "View and manage appointments",
    "dashboard-customers": "Manage your customer relationships",
    "dashboard-calendar": "Schedule and organize appointments",
    "dashboard-analytics": "Business insights and reports",
    "dashboard-profile": "Update your profile information",
    "dashboard-settings": "Configure your account and preferences",
  };

  return subtitleMap[routeName] || "";
});

// Methods
const handleLogout = () => {
  showMobileUserMenu.value = false;
  emit("logout");
};

onMounted(() => {
  const handleClickOutside = (event: Event) => {
    const target = event.target as HTMLElement;
    if (!target.closest(".relative")) {
      showMobileUserMenu.value = false;
    }
  };

  document.addEventListener("click", handleClickOutside);

  onUnmounted(() => {
    document.removeEventListener("click", handleClickOutside);
  });
});
</script>
